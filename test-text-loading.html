<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل النصوص - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .current-text {
            font-weight: bold;
            color: #007bff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">اختبار تحميل النصوص من Firebase</h1>
        
        <div class="test-result info">
            <strong>العنوان الحالي:</strong> 
            <span id="current-title" class="current-text">السلامات لزجاج السيارات</span>
        </div>
        
        <div class="test-result info">
            <strong>العنوان الفرعي الحالي:</strong> 
            <span id="current-subtitle" class="current-text">رائدة في زجاج السيارات</span>
        </div>
        
        <div id="test-results"></div>
        
        <button onclick="testFirebaseConnection()">اختبار الاتصال بـ Firebase</button>
        <button onclick="testSiteContentLoad()">اختبار تحميل siteContent</button>
        <button onclick="testSiteTextsLoad()">اختبار تحميل siteTexts</button>
        <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();
        
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        async function testFirebaseConnection() {
            clearResults();
            addTestResult('🔄 اختبار الاتصال بـ Firebase...', 'info');
            
            try {
                const testRef = database.ref('.info/connected');
                const snapshot = await testRef.once('value');
                const connected = snapshot.val();
                
                if (connected) {
                    addTestResult('✅ الاتصال بـ Firebase ناجح', 'success');
                } else {
                    addTestResult('❌ لا يوجد اتصال بـ Firebase', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في الاتصال بـ Firebase: ${error.message}`, 'error');
            }
        }
        
        async function testSiteContentLoad() {
            addTestResult('🔄 اختبار تحميل siteContent...', 'info');
            
            try {
                const snapshot = await database.ref('siteContent').once('value');
                const data = snapshot.val();
                
                if (data) {
                    addTestResult('✅ تم تحميل siteContent بنجاح', 'success');
                    addTestResult(`📝 العنوان العربي: ${data.titleAr || data.title || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الإنجليزي: ${data.titleEn || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الفرعي العربي: ${data.subtitleAr || data.subtitle || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الفرعي الإنجليزي: ${data.subtitleEn || 'غير محدد'}`, 'info');
                    
                    // Update current display
                    document.getElementById('current-title').textContent = data.titleAr || data.title || 'السلامات لزجاج السيارات';
                    document.getElementById('current-subtitle').textContent = data.subtitleAr || data.subtitle || 'رائدة في زجاج السيارات';
                } else {
                    addTestResult('⚠️ لا توجد بيانات في siteContent', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في تحميل siteContent: ${error.message}`, 'error');
            }
        }
        
        async function testSiteTextsLoad() {
            addTestResult('🔄 اختبار تحميل siteTexts/main...', 'info');
            
            try {
                const snapshot = await database.ref('siteTexts/main').once('value');
                const data = snapshot.val();
                
                if (data) {
                    addTestResult('✅ تم تحميل siteTexts/main بنجاح', 'success');
                    addTestResult(`📝 العنوان العربي: ${data.companyTitleAr || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الإنجليزي: ${data.companyTitleEn || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الفرعي العربي: ${data.companySubtitleAr || 'غير محدد'}`, 'info');
                    addTestResult(`📝 العنوان الفرعي الإنجليزي: ${data.companySubtitleEn || 'غير محدد'}`, 'info');
                } else {
                    addTestResult('⚠️ لا توجد بيانات في siteTexts/main', 'error');
                }
            } catch (error) {
                addTestResult(`❌ خطأ في تحميل siteTexts/main: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            clearResults();
            addTestResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            await testFirebaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSiteContentLoad();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSiteTextsLoad();
            
            addTestResult('✅ انتهت جميع الاختبارات', 'success');
        }
        
        // Run initial test
        setTimeout(runAllTests, 1000);
    </script>
</body>
</html>
